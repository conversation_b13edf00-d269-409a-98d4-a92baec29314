/* ===========
 * 作者: <PERSON><PERSON><PERSON><PERSON>
 * 创建时间: 2025/03/13
 * 非作者最新修改时间：2025/03/20
 * =========== */
using System;
using System.Collections.Generic;
using System.Linq;
using Cysharp.Threading.Tasks;
using Unity.Passport.Runtime;
using UnityEngine;
using UnityEngine.SceneManagement;
using VIC.Core.Assistant;
using VIC.Game.Managers;
using VIC.Game.Operation;
using VIC.Launcher;
using VIC.Networking.Server;
using VIC.Networking.UOS;
using VIC.Networking.UOS.UI;
using VIC.Networking.ChaoXing;

namespace VIC.Core
{
    /// <summary>
    /// UOS初始化管理器：负责协调所有UOS相关管理器的初始化顺序和依赖关系。
    /// 对于【AppResourceManager、MultiGameLauncher、GameLibraryManager、GameManager】仅在跳转到 UOSMainLauncher 场景时初始化，
    /// 且每次进入该场景都重新初始化。
    /// </summary>
    public class InitializationManager_UOS : MonoBehaviour
    {
        #region 单例实现
        public static InitializationManager_UOS Instance { get; private set; }
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                SceneManager.sceneLoaded += OnSceneLoaded;
                // 开始全局初始化
                InitializeAsync().Forget();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        #endregion

        #region 初始化状态与事件
        [Serializable]
        public enum InitState { NotStarted, InProgress, Completed, Failed }
        private Dictionary<string, InitState> initStates = new Dictionary<string, InitState>();
        public event Action<float, string> OnInitializationProgress;
        public event Action OnInitializationCompleted;
        public bool IsInitialized { get; private set; }
        public float InitProgress { get; private set; }

        // 获取初始化状态
        public InitState GetInitState(string managerName)
        {
            return initStates.TryGetValue(managerName, out InitState state) ? state : InitState.NotStarted;
        }

        // 设置初始化状态
        private void SetInitState(string managerName, InitState state)
        {
            initStates[managerName] = state;
        }
        #endregion

        #region 全局初始化流程
        /// <summary>
        /// 异步初始化全局模块（不依赖场景管理器的部分）。
        /// </summary>
        private async UniTaskVoid InitializeAsync()
        {
            initStates.Clear();
            IsInitialized = false;
            InitProgress = 0f;

            // 定义全局模块及其依赖关系
            var globalDependencies = new Dictionary<string, string[]>
            {
                { "UnityMainThreadDispatcher", new string[0] },
                { "PassportFeatureSDK", new string[0] },  // 添加PassportFeatureSDK作为基础依赖
                { "CloudSaveManagement_UOS", new[] { "PassportFeatureSDK" } },
                { "UserDataManager_UOS", new string[0] },
                { "TradingSystemManagement_UOS", new[] { "PassportFeatureSDK" } }  // 依赖PassportFeatureSDK
            };

            // 按依赖关系进行初始化
            await ProcessInitializationSteps(globalDependencies, InitializeGlobalManager);
            IsInitialized = true;
            OnInitializationCompleted?.Invoke();
        }

        /// <summary>
        /// 根据给定的依赖关系字典和初始化委托，执行模块初始化的公共逻辑。
        /// </summary>
        /// <param name="dependencies">模块依赖关系字典</param>
        /// <param name="initFunction">具体的模块初始化方法</param>
        private async UniTask ProcessInitializationSteps(
            Dictionary<string, string[]> dependencies,
            Func<string, UniTask> initFunction)
        {
            // 将所有模块状态置为 NotStarted
            foreach (var entry in dependencies)
            {
                SetInitState(entry.Key, InitState.NotStarted);
            }
            int totalSteps = dependencies.Count;
            int completedSteps = 0;
            List<string> initOrder = TopologicalSort(dependencies);

            foreach (string managerName in initOrder)
            {
                if (!dependencies[managerName].All(dep => GetInitState(dep) == InitState.Completed))
                {
                    SetInitState(managerName, InitState.Failed);
                    Debug.LogWarning($"模块 {managerName} 的依赖未满足，初始化失败");
                    continue;
                }
                SetInitState(managerName, InitState.InProgress);
                OnInitializationProgress?.Invoke((float)completedSteps / totalSteps, $"初始化 {managerName}...");
                try
                {
                    await initFunction(managerName);
                    SetInitState(managerName, InitState.Completed);
                }
                catch (Exception ex)
                {
                    SetInitState(managerName, InitState.Failed);
                    Debug.LogError($"{managerName} 初始化失败: {ex.Message}");
                }
                completedSteps++;
                InitProgress = (float)completedSteps / totalSteps;
                OnInitializationProgress?.Invoke(InitProgress, $"{managerName} 初始化完成");
                await UniTask.Yield();
            }
        }

        /// <summary>
        /// 全局模块的具体初始化逻辑。
        /// </summary>
        private async UniTask InitializeGlobalManager(string managerName)
        {
            switch (managerName)
            {
                case "PassportFeatureSDK":
                    // 统一初始化PassportFeatureSDK
                    Debug.Log("初始化PassportFeatureSDK");
                    try
                    {
                        await PassportFeatureSDK.Initialize();
                        Debug.Log("PassportFeatureSDK初始化成功");
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"PassportFeatureSDK初始化失败: {ex.Message}");
                        throw;
                    }
                    break;
                case "UnityMainThreadDispatcher":
                    // 确保主线程调度器存在
                    if (UnityMainThreadDispatcher.Instance() == null)
                    {
                        Debug.LogError("UnityMainThreadDispatcher实例不存在");
                    }
                    break;
                case "CloudSaveManagement_UOS":
                    // 初始化云端存储管理器（包含默认头像预加载）
                    Debug.Log("初始化CloudSaveManagement_UOS");
                    await CloudSaveManagement_UOS.Initialize();
                    break;
                case "UserDataManager_UOS":
                    // 初始化用户数据管理器
                    Debug.Log("初始化UserDataManager_UOS");
                    UserDataManager_UOS.Initialize();
                    break;
                case "TradingSystemManagement_UOS":
                    // 移除内部的PassportFeatureSDK.Initialize()调用
                    Debug.Log("初始化TradingSystemManagement_UOS");
                    await TradingSystemManagement_UOS.Initialize();
                    break;

                // case "UserManager_ChaoXing":
                //     Debug.Log("初始化UserManager_ChaoXing");
                //     UserDataManager_ChaoXing.Initialize();
                //     break;
                default:
                    Debug.LogWarning($"未知的全局管理器: {managerName}");
                    break;
            }
        }
        // 外部用户登录事件处理
        // private void OnUserLoggedIn()
        // {
        //     Debug.Log("外部用户已登录，开始初始化UOS场景相关管理器");
        //     Debug.Log("UserAccount:" + UserDataManager.CurrentUserData.Account);

        //     // DemoUIControllerNOVA.Instance.StartGame();
        // }
        private void OnDestroy()
        {
            SceneManager.sceneLoaded -= OnSceneLoaded;
        }
        #endregion

        #region 场景切换相关
        private void OnSceneLoaded(Scene scene, LoadSceneMode mode)
        {
            if (scene.name.Equals("UOSMainLauncher", StringComparison.OrdinalIgnoreCase))
            {
                Debug.Log($"加载场景 {scene.name}，开始初始化UOS场景相关管理器");
                InitializeSceneManagers().Forget();
            }
        }

        /// <summary>
        /// 初始化场景相关的管理器，每次进入 UOSMainLauncher 场景时调用
        /// </summary>
        private async UniTaskVoid InitializeSceneManagers()
        {
            // 定义场景模块及其依赖
            var sceneDependencies = new Dictionary<string, string[]>
            {
                { "AppResourceManager", new string[0] },
                { "MultiGameLauncher", new string[0] },
                { "GameLibraryManager", new [] { "MultiGameLauncher" } },
                { "GameManager", new [] { "MultiGameLauncher", "GameLibraryManager" } },
                { "GameSearchManager", new [] { "GameManager" } },
                // { "AppServerManager", new string[0] },
                { "LauncherPipeReceiver", new string[0] }
            };

            await ProcessInitializationSteps(sceneDependencies, InitializeSceneManager);
        }

        private async UniTask InitializeSceneManager(string managerName)
        {
            switch (managerName)
            {
                case "AppResourceManager":
                    if (AppResourceManager.Instance != null)
                    {
                        AppResourceManager.Instance.InitializeManager();
                        await AppResourceManager.Instance.PreloadAllGameResourcesAsync();
                    }
                    else
                    {
                        throw new Exception("AppResourceManager 实例不存在");
                    }
                    break;
                case "MultiGameLauncher":
                    if (MultiGameLauncher.Instance != null)
                    {
                        await MultiGameLauncher.Instance.InitializeAsync();
                    }
                    else
                    {
                        throw new Exception("MultiGameLauncher 实例不存在");
                    }
                    break;
                case "GameLibraryManager":
                    if (GameLibraryManager.Instance != null)
                    {
                        GameLibraryManager.Instance.InitializeManagerAsync().Forget();
                        // await UniTask.Delay(500);
                        // GameLibraryManager.Instance.LoadOwnedGamesFromCloud();
                    }
                    else
                    {
                        throw new Exception("GameLibraryManager 实例不存在");
                    }
                    break;
                case "GameManager":
                    if (GameManager.Instance != null)
                    {
                        await GameManager.Instance.InitializeAsync();
                    }
                    else
                    {
                        throw new Exception("GameManager 实例不存在");
                    }
                    break;
                case "GameSearchManager":
                    if (GameSearchManager.Instance != null)
                    {
                        GameSearchManager.Instance.InitializeManager();
                    }
                    else
                    {
                        throw new Exception("GameSearchManager 实例不存在");
                    }
                    break;
                // case "AppServerManager":
                //     if (AppServerManager.Instance != null)
                //     {
                //         AppServerManager.Instance.InitializeManager();
                //     }
                //     else
                //     {
                //         throw new Exception("AppServerManager 实例不存在");
                //     }
                //     break;
                case "LauncherPipeReceiver":
                    if (LauncherPipeReceiver.Instance != null)
                    {
                        LauncherPipeReceiver.Instance.InitializeManager();
                    }
                    else
                    {
                        throw new Exception("LauncherPipeReceiver 实例不存在");
                    }
                    break;
                default:
                    Debug.LogWarning($"未知的场景管理器: {managerName}");
                    break;
            }
        }
        #endregion

        #region 拓扑排序
        /// <summary>
        /// 对依赖关系进行拓扑排序，确保按正确顺序初始化
        /// </summary>
        private List<string> TopologicalSort(Dictionary<string, string[]> dependencies)
        {
            var graph = new Dictionary<string, List<string>>();
            var inDegree = new Dictionary<string, int>();

            // 构建图及入度统计
            foreach (var entry in dependencies)
            {
                string node = entry.Key;
                if (!graph.ContainsKey(node))
                {
                    graph[node] = new List<string>();
                    inDegree[node] = 0;
                }
                foreach (string dep in entry.Value)
                {
                    if (!graph.ContainsKey(dep))
                    {
                        graph[dep] = new List<string>();
                        inDegree[dep] = 0;
                    }
                    graph[dep].Add(node);
                    inDegree[node]++;
                }
            }

            // 拓扑排序
            var queue = new Queue<string>();
            foreach (var kv in inDegree)
            {
                if (kv.Value == 0)
                    queue.Enqueue(kv.Key);
            }

            var result = new List<string>();
            while (queue.Count > 0)
            {
                string cur = queue.Dequeue();
                result.Add(cur);
                if (graph.TryGetValue(cur, out List<string> neighbors))
                {
                    foreach (string neighbor in neighbors)
                    {
                        inDegree[neighbor]--;
                        if (inDegree[neighbor] == 0)
                        {
                            queue.Enqueue(neighbor);
                        }
                    }
                }
            }
            if (result.Count != dependencies.Count)
                Debug.LogError("存在循环依赖，无法确定初始化顺序");
            return result;
        }
        #endregion
    }
}
