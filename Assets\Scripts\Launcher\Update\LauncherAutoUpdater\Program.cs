using System;
using System.Diagnostics;
using System.IO;
using System.Windows.Forms;
using AutoUpdaterDotNET;

namespace UpdateTool
{
    static class Program
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            AutoUpdater.DownloadPath = Path.Combine(Application.StartupPath, "UpdateTemp");
            AutoUpdater.ApplicationExitEvent += AutoUpdater_ApplicationExitEvent;
            AutoUpdater.Start("https://example.com/launcher_version.xml");

            Application.Run(); // ������Ϣѭ��
        }

        private static void AutoUpdater_ApplicationExitEvent()
        {
            try
            {
                // ������ɺ���� Unity Launcher
                string launcherPath = Path.Combine(Application.StartupPath, "UnityLauncher.exe");
                if (File.Exists(launcherPath))
                {
                    Process.Start(launcherPath);
                }
            }
            catch { }
            Environment.Exit(0);
        }
    }
}
